<template>
  <div class="http-stream-player-container">
    <div class="http-stream-player" :class="{ 'is-playing': isPlaying, 'compact-mode': true, 'empty-state': !streamUrl }">
      <div class="player-content">
        <!-- 播放/暂停按钮 -->
        <el-button 
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" 
          circle 
          size="small"
          class="control-btn play-btn"
          @click="togglePlay"
          :disabled="!streamUrl"
        />
        
        <!-- 状态显示 -->
        <div class="status-container">
          <div class="status-indicator" :class="connectionStatus">
            {{ statusText }}
          </div>
          
          <!-- 连接时长显示 -->
          <div class="connection-time" v-if="isPlaying && connectionDuration > 0">
            {{ formatTime(connectionDuration) }}
          </div>
        </div>
        
        <!-- 音量控制 -->
        <div class="volume-control" @mouseleave="hideVolumeSlider">
          <el-button 
            :icon="volume === 0 ? 'Mute' : 'Microphone'" 
            circle 
            size="small"
            class="control-btn volume-btn"
            @click="toggleVolumeControl"
            :disabled="!streamUrl"
          />
          <div class="volume-slider-container" v-show="showVolumeSlider">
            <el-slider 
              v-model="volume" 
              :max="100"
              class="volume-slider"
              @change="handleVolumeChange"
              vertical
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 音频名称显示 -->
    <div class="audio-name-display" v-if="audioName && streamUrl">
      <el-tooltip :content="audioName" placement="top" :show-after="500">
        <span class="audio-name-text">{{ audioName }}</span>
      </el-tooltip>
    </div>
    
    <audio
      ref="audioEl"
      :src="streamUrl"
      preload="auto"
      @error="onError"
      @play="onPlay"
      @pause="onPause"
      @canplay="onCanPlay"
      @waiting="onWaiting"
      @playing="onPlaying"
      @stalled="onStalled"
      @suspend="onSuspend"
      @abort="onAbort"
    ></audio>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  streamUrl: {
    type: String,
    required: true,
    default: ''
  },
  audioName: {
    type: String,
    default: '实时音频流'
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  showStats: {
    type: Boolean,
    default: false
  },
  reconnectAttempts: {
    type: Number,
    default: 3
  },
  reconnectInterval: {
    type: Number,
    default: 5000
  }
})

const emit = defineEmits([
  'play', 
  'pause', 
  'error', 
  'connected', 
  'disconnected', 
  'waiting', 
  'stalled',
  'reconnecting',
  'reconnected',
  'reconnect-failed'
])

// 状态变量
const audioEl = ref(null)
const isPlaying = ref(false)
const volume = ref(100)
const lastVolume = ref(100)
const showVolumeSlider = ref(false)
const connectionStatus = ref('disconnected') // 'connected', 'connecting', 'disconnected', 'error', 'reconnecting'
const bufferingState = ref('未缓冲')
const connectionStartTime = ref(null)
const connectionDuration = ref(0)
const reconnectCount = ref(0)
const reconnectTimer = ref(null)

// 连接状态文本
const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'disconnected':
      return '未连接'
    case 'error':
      return '连接错误'
    case 'reconnecting':
      return `重连中(${reconnectCount.value}/${props.reconnectAttempts})`
    default:
      return '未知状态'
  }
})

// 播放控制
const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

// 播放
const play = async () => {
  if (!props.streamUrl) {
    ElMessage.warning('未提供音频流地址')
    return
  }
  
  connectionStatus.value = 'connecting'
  
  try {
    await audioEl.value.play()
    startConnectionTimer()
  } catch (error) {
    console.error('播放失败:', error)
    connectionStatus.value = 'error'
    
    // 转换错误信息为友好的中文提示
    const errorMessage = getChineseErrorMessage(error)
    
    emit('error', { 
      originalError: error,
      message: errorMessage
    })
    
    ElMessage.error(`播放失败: ${errorMessage}`)
    
    // 尝试自动重连
    if (props.reconnectAttempts > 0) {
      startReconnect()
    }
  }
}

// 错误信息转换为中文
const getChineseErrorMessage = (error) => {
  const errorMessage = error.message || '未知错误'
  
  // 常见错误类型转换
  if (errorMessage.includes('NotSupportedError') || errorMessage.includes('Failed to load')) {
    return '音频格式不支持或资源无法访问'
  }
  
  if (errorMessage.includes('NotAllowedError')) {
    return '浏览器禁止自动播放，请手动点击播放按钮'
  }
  
  if (errorMessage.includes('AbortError')) {
    return '播放被中断'
  }
  
  if (errorMessage.includes('NetworkError') || errorMessage.includes('Bad Gateway')) {
    return '网络错误，无法连接到音频服务器'
  }
  
  if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
    return '连接超时，请检查网络状态'
  }
  
  return '音频播放出错，请稍后再试'
}

// 暂停
const pause = () => {
  if (audioEl.value) {
    audioEl.value.pause()
    connectionStatus.value = 'disconnected'
    stopConnectionTimer()
  }
}

// 重新连接
const startReconnect = () => {
  // 清除之前的重连计时器
  if (reconnectTimer.value) {
    clearTimeout(reconnectTimer.value)
  }
  
  reconnectCount.value = 0
  attemptReconnect()
}

const attemptReconnect = () => {
  if (reconnectCount.value >= props.reconnectAttempts) {
    connectionStatus.value = 'error'
    emit('reconnect-failed')
    ElMessage.error(`重连失败，已尝试 ${props.reconnectAttempts} 次`)
    return
  }
  
  reconnectCount.value++
  connectionStatus.value = 'reconnecting'
  emit('reconnecting', reconnectCount.value)
  
  reconnectTimer.value = setTimeout(() => {
    if (audioEl.value) {
      audioEl.value.load()
      play()
        .then(() => {
          emit('reconnected')
          ElMessage.success('重连成功')
        })
        .catch(() => {
          attemptReconnect()
        })
    }
  }, props.reconnectInterval)
}

// 连接计时器
const startConnectionTimer = () => {
  connectionStartTime.value = Date.now()
  updateConnectionDuration()
}

const stopConnectionTimer = () => {
  connectionStartTime.value = null
  connectionDuration.value = 0
}

const updateConnectionDuration = () => {
  if (!connectionStartTime.value) return
  
  connectionDuration.value = Math.floor((Date.now() - connectionStartTime.value) / 1000)
  setTimeout(updateConnectionDuration, 1000)
}

// 显示/隐藏音量控制滑块
const toggleVolumeControl = () => {
  if (!props.streamUrl) return
  showVolumeSlider.value = !showVolumeSlider.value
}

// 隐藏音量滑块
const hideVolumeSlider = () => {
  showVolumeSlider.value = false
}

// 音量控制
const handleVolumeChange = (val) => {
  if (audioEl.value) {
    audioEl.value.volume = val / 100
    lastVolume.value = val
  }
}

// 静音切换
const toggleMute = () => {
  if (volume.value > 0) {
    lastVolume.value = volume.value
    volume.value = 0
  } else {
    volume.value = lastVolume.value
  }
  handleVolumeChange(volume.value)
}

// 事件处理
const onPlay = () => {
  isPlaying.value = true
  emit('play')
}

const onPause = () => {
  isPlaying.value = false
  emit('pause')
}

const onError = (error) => {
  console.error('音频流错误:', error)
  connectionStatus.value = 'error'
  
  // 获取错误详情
  const errorCode = error.target?.error?.code
  const errorMessage = error.target?.error?.message
  
  // 转换为友好的中文错误信息
  let chineseErrorMessage = '音频播放出错，请稍后再试'
  
  if (errorCode === 1) {
    chineseErrorMessage = '音频加载被中断'
  } else if (errorCode === 2) {
    chineseErrorMessage = '网络错误，无法加载音频'
  } else if (errorCode === 3) {
    chineseErrorMessage = '音频解码失败，不支持的格式'
  } else if (errorCode === 4) {
    chineseErrorMessage = '音频资源不可用或格式不支持'
  } else if (errorMessage) {
    chineseErrorMessage = getChineseErrorMessage({ message: errorMessage })
  }
  
  // 发送错误事件
  emit('error', {
    originalError: error,
    code: errorCode,
    message: chineseErrorMessage
  })
  
  stopConnectionTimer()
  
  // 尝试自动重连
  if (props.reconnectAttempts > 0) {
    startReconnect()
  } else {
    ElMessage.error(`音频流错误: ${chineseErrorMessage}`)
  }
}

const onCanPlay = () => {
  bufferingState.value = '已缓冲'
}

const onWaiting = () => {
  bufferingState.value = '缓冲中...'
  emit('waiting')
}

const onPlaying = () => {
  connectionStatus.value = 'connected'
  emit('connected')
}

const onStalled = () => {
  bufferingState.value = '已停滞'
  emit('stalled')
}

const onSuspend = () => {
  if (isPlaying.value) {
    bufferingState.value = '已暂停下载'
  }
}

const onAbort = () => {
  connectionStatus.value = 'disconnected'
  emit('disconnected')
  stopConnectionTimer()
}

// 工具函数
const formatTime = (seconds) => {
  if (!seconds) return '00:00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':')
}

// 监听音量变化
watch(volume, (newVal) => {
  if (audioEl.value) {
    audioEl.value.volume = newVal / 100
  }
})

// 监听 streamUrl 变化
watch(() => props.streamUrl, (newUrl, oldUrl) => {
  if (newUrl && newUrl !== oldUrl && audioEl.value) {
    // 重置状态
    connectionStatus.value = 'disconnected'
    bufferingState.value = '未缓冲'
    stopConnectionTimer()
    
    // 如果当前正在播放，则重新加载并播放
    const wasPlaying = isPlaying.value
    
    // 清除重连计时器
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
    
    reconnectCount.value = 0
    audioEl.value.load()
    
    if (wasPlaying || props.autoplay) {
      connectionStatus.value = 'connecting'
      play()
    }
  }
})

// 组件挂载
onMounted(() => {
  if (audioEl.value) {
    // 设置初始音量
    audioEl.value.volume = volume.value / 100
    
    // 如果设置了自动播放，则尝试播放
    if (props.autoplay && props.streamUrl) {
      play()
    }
  }
})

// 组件卸载前
onBeforeUnmount(() => {
  if (reconnectTimer.value) {
    clearTimeout(reconnectTimer.value)
    reconnectTimer.value = null
  }
})

// 组件卸载
onUnmounted(() => {
  if (audioEl.value && isPlaying.value) {
    audioEl.value.pause()
    audioEl.value.src = ''
  }
  stopConnectionTimer()
})

// 导出方法供父组件调用
defineExpose({
  play,
  pause,
  isPlaying,
  connectionStatus,
  reconnect: startReconnect
})
</script>

<style scoped>
.http-stream-player-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  margin: 0;
}

.http-stream-player {
  background: #f0f2f5;
  border-radius: 20px;
  padding: 0 12px;
  box-shadow: var(--el-box-shadow-lighter);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

.compact-mode {
  display: flex;
  width: 304px;
  height: 58px;
  align-items: center;
}

.player-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 0;
}

.control-btn {
  flex-shrink: 0;
}

.play-btn, .volume-btn {
  font-size: 12px;
  width: 32px;
  height: 32px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  overflow: hidden;
}

.status-indicator {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

.status-indicator.connected {
  background-color: var(--el-color-success);
  color: white;
}

.status-indicator.connecting {
  background-color: var(--el-color-warning);
  color: white;
}

.status-indicator.disconnected {
  background-color: var(--el-color-info);
  color: white;
}

.status-indicator.error {
  background-color: var(--el-color-danger);
  color: white;
}

.status-indicator.reconnecting {
  background-color: var(--el-color-warning);
  color: white;
  animation: blink 1s infinite;
}

.connection-time {
  font-size: 12px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.volume-control {
  display: flex;
  align-items: center;
  position: relative;
}

.volume-slider-container {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  padding: 12px 8px;
  box-shadow: var(--el-box-shadow-light);
  z-index: 10;
  height: 120px;
  width: 40px;
  display: flex;
  justify-content: center;
}

.volume-slider {
  height: 100px;
}

.play-btn {
  position: relative;
}

.is-playing .play-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  animation: ripple 1s infinite;
  z-index: -1;
}

/* 音频名称显示样式 */
.audio-name-display {
  margin-top: 8px;
  padding: 0 4px;
  width: 100%;
  text-align: center;
}

.audio-name-text {
  font-size: 13px;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 100%;
  cursor: default;
}

/* 空状态样式 */
.empty-state .control-btn {
  opacity: 0.6;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 自定义音量滑块样式 */
:deep(.el-slider--vertical) {
  height: 100px;
}

:deep(.el-slider--vertical .el-slider__runway) {
  width: 4px;
  height: 100%;
}

:deep(.el-slider--vertical .el-slider__bar) {
  width: 4px;
}

:deep(.el-slider--vertical .el-slider__button-wrapper) {
  width: 24px;
  height: 24px;
  left: -10px;
  top: auto;
}

:deep(.el-slider--vertical .el-slider__button) {
  width: 12px;
  height: 12px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .compact-mode {
    min-width: 240px;
  }
  
  .status-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media screen and (max-width: 576px) {
  .compact-mode {
    min-width: 200px;
  }
}
</style> 