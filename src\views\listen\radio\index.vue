<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="query-form">
        <el-form-item label="电台编号" prop="id">
          <el-input
            v-model="queryParams.id"
            placeholder="请输入电台编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="电台名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入电台名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="语种分类" prop="language">
          <el-select
            v-model="queryParams.language"
            placeholder="请选择语种分类"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="中文" :value="0" />
            <el-option label="英文" :value="1" />
            <el-option label="法语" :value="2" />
            <el-option label="日语" :value="3" />
            <el-option label="德语" :value="4" />
            <el-option label="西班牙语" :value="5" />
            <el-option label="意大利语" :value="6" />
            <el-option label="俄语" :value="7" />
            <el-option label="韩语" :value="8" />
            <el-option label="葡萄牙语" :value="9" />
            <el-option label="阿拉伯语" :value="10" />
            <el-option label="希腊语" :value="11" />
          </el-select>
        </el-form-item>
        <el-form-item label="电台状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" style="width: 300px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-container">
      <div class="button-group">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
      
      <!-- 拉流播放器 -->
      <div class="player-wrapper">
        <http-stream-player
          :stream-url="currentRadio.streamUrl"
          :audio-name="currentRadio.name || '暂无播放内容'"
          :autoplay="false"
          :show-stats="false"
          :hide-url="true"
          @play="handleAudioPlay"
          @pause="handleAudioPause"
          @error="handleAudioError"
          ref="streamPlayerRef"
        />
        <div v-if="!currentRadio.radioId" class="player-overlay"></div>
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['listen:radio:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="radioList" @selection-change="handleSelectionChange" style="width: 100%" :row-class-name="getRowClassName">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="电台编号" align="center" prop="id" width="200" />
      <el-table-column label="电台名称" prop="name" :show-overflow-tooltip="true" min-width="120">
        <template #default="scope">
          <span class="radio-name-cell" :title="scope.row.name">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="简介" prop="description" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="封面图" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row.coverUrl" class="image-container" @click="previewRadioImages(scope.row, 'cover')">
            <el-image
              :src="scope.row.coverUrl"
              class="table-image cover-image-preview"
              fit="cover"
            >
              <template #placeholder>
                <div class="image-placeholder">
                  <el-icon><Loading /></el-icon>
                  <span>加载中</span>
                </div>
              </template>
              <template #error>
                <div class="image-error">
                  <el-icon><PictureFilled /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            <div class="image-hover-text">
              <el-icon style="margin-right: 4px;"><ZoomIn /></el-icon>
              点击预览
            </div>
          </div>
          <span v-else>无封面</span>
        </template>
      </el-table-column>
      <el-table-column label="电台背景图" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row.backGroundUrl" class="image-container" @click="previewRadioImages(scope.row, 'background')">
            <el-image
              :src="scope.row.backGroundUrl"
              class="table-image bg-image-preview"
              fit="cover"
            >
              <template #placeholder>
                <div class="image-placeholder">
                  <el-icon><Loading /></el-icon>
                  <span>加载中</span>
                </div>
              </template>
              <template #error>
                <div class="image-error">
                  <el-icon><PictureFilled /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            <div class="image-hover-text">
              <el-icon style="margin-right: 4px;"><ZoomIn /></el-icon>
              点击预览
            </div>
          </div>
          <span v-else>无背景图</span>
        </template>
      </el-table-column>
      <el-table-column label="语种分类" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '中文', value: 0, elTagType: 'primary' },
            { label: '英文', value: 1, elTagType: 'info' },
            { label: '法语', value: 2, elTagType: 'success' },
            { label: '日语', value: 3, elTagType: 'warning' },
            { label: '德语', value: 4, elTagType: 'danger' },
            { label: '西班牙语', value: 5, elTagType: 'primary' },
            { label: '意大利语', value: 6, elTagType: 'info' },
            { label: '俄语', value: 7, elTagType: 'success' },
            { label: '韩语', value: 8, elTagType: 'warning' },
            { label: '葡萄牙语', value: 9, elTagType: 'danger' },
            { label: '阿拉伯语', value: 10, elTagType: 'primary' },
            { label: '希腊语', value: 11, elTagType: 'info' }
          ]" :value="scope.row.language" />
        </template>
      </el-table-column>
      <el-table-column label="推荐位排序" align="center" prop="sort" width="100" />
      <el-table-column label="电台状态" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: 1, elTagType: 'success' },
            { label: '禁用', value: 0, elTagType: 'danger' }
          ]" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="电台推流状态" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '已上架', value: 1, elTagType: 'success' },
            { label: '未上架', value: 0, elTagType: 'info' }
          ]" :value="scope.row.playing || 0" />
        </template>
      </el-table-column>
      <el-table-column label="置顶状态" align="center" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.top === 1" type="primary" effect="dark" class="top-tag">
            <el-icon class="top-icon-el"><Top /></el-icon>
            <span>已置顶</span>
          </el-tag>
          <el-tag v-else type="info" effect="plain" class="no-top-tag">未置顶</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="音频数量" align="center" prop="audioNum" width="80" />
      <el-table-column label="电台播放量" align="center" prop="playTimes" width="80" />
      <el-table-column label="创建时间" align="center" prop="created" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updated" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updated) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" width="100">
        <template #default="scope">
          <el-tooltip :content="currentRadio.radioId === scope.row.id && isPlaying ? '暂停' : '播放'" placement="top">
            <el-button 
              circle 
              :icon="currentRadio.radioId === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
              @click="playRadio(scope.row)"
              size="small"
              :disabled="scope.row.status === 0"
              :type="scope.row.status === 0 ? 'info' : (currentRadio.radioId === scope.row.id && isPlaying ? 'danger' : 'primary')"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:radio:edit']"></el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.status === 0 ? '启用' : '禁用'" placement="top">
            <el-button
              link
              type="primary"
              :icon="scope.row.status === 0 ? 'Check' : 'Close'"
              v-if="scope.row.status === 0 || scope.row.status === 1"
              @click="handleStatusChange(scope.row, scope.row.status === 0 ? 1 : 0)"
              v-hasPermi="['system:radio:edit']"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.playing === 0 ? '上架' : '下架'" placement="top">
            <el-button
              link
              type="primary"
              :icon="scope.row.playing === 0 ? 'Upload' : 'Download'"
              @click="handlePlayingChange(scope.row, scope.row.playing === 0 ? 1 : 0)"
              v-hasPermi="['system:radio:edit']"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.top === 0 || scope.row.top === undefined ? '置顶' : '取消置顶'" placement="top">
            <el-button
              link
              :type="scope.row.top === 1 ? 'warning' : 'primary'"
              @click="handleTopChange(scope.row, scope.row.top === 0 || scope.row.top === undefined ? 1 : 0)"
              v-hasPermi="['system:radio:edit']"
              class="top-button"
            >
              <el-icon :class="{'top-icon-active': scope.row.top === 1}">
                <component :is="scope.row.top === 0 || scope.row.top === undefined ? Top : Bottom" />
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:radio:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加/修改电台对话框 -->
    <el-dialog :title="title" v-model="open" width="550px" append-to-body>
      <el-form ref="radioFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="电台名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入电台名称" />
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input v-model="form.description" placeholder="请输入简介" />
        </el-form-item>
        <el-form-item label="上传封面" prop="coverFile">
          <div class="upload-wrapper">
            <el-upload
              class="cover-uploader"
              action="#"
              :http-request="uploadCoverRequest"
              :show-file-list="false"
              :before-upload="beforeCoverUpload"
            >
              <img v-if="form.coverUrl" :src="form.coverUrl" class="cover-image" />
              <div v-else class="cover-upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div>上传封面</div>
              </div>
            </el-upload>
            <div class="upload-tip">只支持 .jpg 格式</div>
          </div>
        </el-form-item>
        <el-form-item label="上传背景图" prop="backGroundFile">
          <div class="upload-wrapper">
            <el-upload
              class="cover-uploader"
              action="#"
              :http-request="uploadBackgroundRequest"
              :show-file-list="false"
              :before-upload="beforeCoverUpload"
            >
              <img v-if="form.backGroundUrl" :src="form.backGroundUrl" class="cover-image" />
              <div v-else class="cover-upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div>上传背景图</div>
              </div>
            </el-upload>
            <div class="upload-tip">只支持 .jpg 格式</div>
          </div>
        </el-form-item>
        <el-form-item label="语种分类" prop="language">
          <el-select v-model="form.language" placeholder="请选择语种分类" style="width: 100%">
            <el-option label="中文" :value="0" />
            <el-option label="英文" :value="1" />
            <el-option label="法语" :value="2" />
            <el-option label="日语" :value="3" />
            <el-option label="德语" :value="4" />
            <el-option label="西班牙语" :value="5" />
            <el-option label="意大利语" :value="6" />
            <el-option label="俄语" :value="7" />
            <el-option label="韩语" :value="8" />
            <el-option label="葡萄牙语" :value="9" />
            <el-option label="阿拉伯语" :value="10" />
            <el-option label="希腊语" :value="11" />
          </el-select>
        </el-form-item>
        <el-form-item label="电台状态" prop="status">
          <el-radio-group v-model="form.status" class="inline-radio-group">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="电台推流状态" prop="playing">
          <el-radio-group v-model="form.playing" class="inline-radio-group">
            <el-radio :label="1">已上架</el-radio>
            <el-radio :label="0">未上架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="置顶状态" prop="top">
          <el-radio-group v-model="form.top" class="inline-radio-group">
            <el-radio :label="1">已置顶</el-radio>
            <el-radio :label="0">未置顶</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加图片预览组件 -->
    <el-image-viewer
      v-if="previewVisible"
      :url-list="previewImages"
      :initial-index="previewIndex"
      :z-index="3000"
      :hide-on-click-modal="false"
      :teleported="true"
      :close-on-press-escape="true"
      @close="previewVisible = false"
    >
      <template #viewer-title>
        <div class="image-preview-title">
          {{ previewIndex === 0 && previewImages.length > 1 ? '封面图' : '背景图' }}
        </div>
      </template>
    </el-image-viewer>
  </div>
</template>

<script setup name="radio">
import { listRadio, getRadio, delRadio, addRadio, updateRadio, changeRadioStatus, changeRadioPlaying, getRadioAudioList, uploadCover, changeRadioTop } from "@/api/listen/radio";
import HttpStreamPlayer from "@/components/HttpStreamPlayer/index.vue";
import { parseTime } from '@/utils/ruoyi';
import { addQueryDateRange } from '@/utils/dateUtils';
import DictTag from '@/components/DictTag/index.vue';
import { onMounted, nextTick, watch } from 'vue';
import { Loading, PictureFilled, Plus, Headset, ZoomIn, StarFilled, Star, Top, Bottom } from '@element-plus/icons-vue';
import { ElImageViewer } from 'element-plus';

const { proxy } = getCurrentInstance();
const route = useRoute();

const radioList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const title = ref("");
const open = ref(false);

const data = reactive({
  form: {
    id: undefined,
    name: '',
    description: '',
    coverUrl: '',
    coverFileId: undefined,
    coverFile: undefined,
    backGroundUrl: '',
    backGroundFileId: undefined,
    backGroundFile: undefined,
    language: 0,
    status: 0,
    playing: 0,
    sort: 0,
    playTimes: 0,
    top: 0
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    name: undefined,
    language: undefined,
    status: undefined,
    playing: undefined,
    hasBackground: undefined
  },
  rules: {
    name: [
      { required: true, message: "电台名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "简介不能为空", trigger: "blur" }
    ],
    language: [
      { required: true, message: "语种分类不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 播放相关
const currentRadio = ref({
  radioId: null,
  name: "",
  streamUrl: "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"
});
const isPlaying = ref(false);

// 图片预览相关
const previewImages = ref([]);
const previewVisible = ref(false);
const previewIndex = ref(0);

// 引用播放器组件
const streamPlayerRef = ref(null);

/** 查询电台列表 */
function getList() {
  loading.value = true;
  
  // 使用新的日期工具函数添加日期范围参数
  const finalParams = addQueryDateRange(queryParams.value, dateRange.value);
  
  listRadio(finalParams).then(response => {
    radioList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error("获取电台列表失败:", error);
    loading.value = false;
  });
}

/** 预览电台图片 */
function previewRadioImages(row, imageType) {
  // 收集所有有效的图片URL
  const images = [];
  let initialIndex = 0;
  
  // 添加封面图
  if (row.coverUrl) {
    images.push(row.coverUrl);
    
    if (imageType === 'cover') {
      initialIndex = 0;
    }
  }
  
  // 添加背景图
  if (row.backGroundUrl) {
    images.push(row.backGroundUrl);
    
    if (imageType === 'background') {
      initialIndex = images.length - 1;
    }
  }
  
  // 如果有图片，显示预览
  if (images.length > 0) {
    // 先设置图片数组，再设置索引，最后显示预览
    previewImages.value = images;
    previewIndex.value = initialIndex;
    
    // 使用nextTick确保DOM已更新
    nextTick(() => {
      previewVisible.value = true;
    });
  } else {
    // 没有图片时提示用户
    proxy.$modal.msgWarning("没有可预览的图片");
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  
  // 确保语种和状态为数字类型
  if (queryParams.value.language !== undefined && queryParams.value.language !== '' && queryParams.value.language !== null) {
    queryParams.value.language = Number(queryParams.value.language);
  }
  
  if (queryParams.value.status !== undefined && queryParams.value.status !== '' && queryParams.value.status !== null) {
    queryParams.value.status = Number(queryParams.value.status);
  }
  
  if (queryParams.value.hasBackground !== undefined && queryParams.value.hasBackground !== '' && queryParams.value.hasBackground !== null) {
    queryParams.value.hasBackground = Number(queryParams.value.hasBackground);
  }
  
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  
  // 确保所有筛选条件都被清空
  queryParams.value.hasBackground = undefined;
  
  // 重置播放器状态
  resetPlayer();
  
  handleQuery();
}

/** 重置播放器 */
function resetPlayer() {
  // 如果正在播放，先暂停
  if (isPlaying.value && streamPlayerRef.value) {
    streamPlayerRef.value.pause();
  }
  
  // 重置所有播放器相关状态，但保留组件显示
  currentRadio.value = {
    radioId: null,
    name: "",
    streamUrl: "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"
  };
  isPlaying.value = false;
  
  // 确保播放器处于暂停状态
  nextTick(() => {
    if (streamPlayerRef.value) {
      streamPlayerRef.value.pause();
    }
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: '',
    description: '',
    coverUrl: '',
    coverFileId: undefined,
    coverFile: undefined,
    backGroundUrl: '',
    backGroundFileId: undefined,
    backGroundFile: undefined,
    language: 0,
    status: 0,
    playing: 0,
    sort: 0,
    playTimes: 0,
    top: 0
  };
  proxy.resetForm("radioFormRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加电台";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const radioId = row.id || ids.value;
  getRadio(radioId).then(response => {
    const radioData = response.data;
    
    // 确保数据类型正确
    if (radioData.language !== undefined && radioData.language !== null) {
      radioData.language = Number(radioData.language);
    }
    
    if (radioData.status !== undefined && radioData.status !== null) {
      radioData.status = Number(radioData.status);
    }
    
    form.value = radioData;
    open.value = true;
    title.value = "修改电台";
  });
}

/** 状态修改 */
function handleStatusChange(row, status) {
  const text = status === 1 ? "启用" : "禁用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"电台吗?').then(function() {
    return changeRadioStatus(row.id, status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList();
  }).catch(() => {});
}

/** 推流状态修改（上下架） */
function handlePlayingChange(row, playing) {
  const text = playing === 1 ? "上架" : "下架";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"电台吗?').then(function() {
    return changeRadioPlaying(row.id, playing);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList();
  }).catch(() => {});
}

/** 置顶状态修改 */
function handleTopChange(row, top) {
  const text = top === 1 ? "置顶" : "取消置顶";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"电台吗?').then(function() {
    return changeRadioTop(row.id, top);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList();
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const radioIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除电台编号为"' + radioIds + '"的数据项?').then(function() {
    return delRadio(radioIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["radioFormRef"].validate(valid => {
    if (valid) {
      // 准备提交的数据，移除不需要的字段
      const submitData = {
        ...form.value,
        coverFile: undefined, // 移除文件对象，不需要提交到后端
        backGroundFile: undefined // 移除背景图文件对象
      };

      // 确保封面地址和路径被正确提交
      if (!submitData.coverUrl) {
        proxy.$modal.msgWarning("请上传电台封面");
        return;
      }

      if (submitData.id != undefined) {
        updateRadio(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRadio(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 封面上传前的校验 */
function beforeCoverUpload(file) {
  const isJPG = file.type === 'image/jpeg';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    proxy.$modal.msgError('上传封面图片只能是 JPG 格式!');
    return false;
  }
  if (!isLt2M) {
    proxy.$modal.msgError('上传封面图片大小不能超过 2MB!');
    return false;
  }
  return true;
}

/** 自定义封面上传 */
function uploadCoverRequest(options) {
  const formData = new FormData();
  formData.append('file', options.file);
  formData.append('picType', 0); // 添加参数，0表示封面图

  // 调用上传接口
  uploadCover(options.file, formData).then(response => {
    form.value.coverUrl = response.url;
    form.value.coverFileId = response.fileId;
    form.value.coverFile = options.file;
    proxy.$modal.msgSuccess("封面上传成功");
  }).catch(error => {
    proxy.$modal.msgError("封面上传失败");
  });
}

/** 自定义背景图上传 */
function uploadBackgroundRequest(options) {
  const formData = new FormData();
  formData.append('file', options.file);
  formData.append('picType', 1); // 添加参数，1表示背景图

  // 调用上传接口
  uploadCover(options.file, formData).then(response => {
    form.value.backGroundUrl = response.url;
    form.value.backGroundFileId = response.fileId;
    form.value.backGroundFile = options.file;
    proxy.$modal.msgSuccess("背景图上传成功");
  }).catch(error => {
    proxy.$modal.msgError("背景图上传失败");
  });
}

/** 试听电台 */
function playRadio(radio) {
  // 如果电台已禁用，不允许播放
  if (radio.status === 0) {
    proxy.$modal.msgWarning("该电台已禁用，无法播放");
    return;
  }
  
  // 如果点击的是当前正在播放的电台，则暂停播放
  if (currentRadio.value.radioId === radio.id && isPlaying.value) {
    if (streamPlayerRef.value) {
      streamPlayerRef.value.pause();
    }
    isPlaying.value = false;
    return;
  }
  
  // 设置loading状态
  loading.value = true;
  
  // 获取电台下所有音频
  getRadioAudioList(radio.id).then(response => {
    const audioList = response.data || [];
    
    // 检查是否有音频
    if (audioList.length === 0) {
      proxy.$modal.msgWarning("该电台暂无可播放的音频");
      loading.value = false;
      return;
    }
    
    // 只获取启用状态的音频(status=1)
    const enabledAudios = audioList.filter(item => item.status === 1);
    
    if (enabledAudios.length === 0) {
      proxy.$modal.msgWarning("该电台暂无可播放的音频");
      loading.value = false;
      return;
    }
    
    // 设置当前电台信息
    currentRadio.value = {
      radioId: radio.id,
      name: radio.name,
      // 使用第一个可用音频的URL作为流媒体URL
      streamUrl: enabledAudios[0].audioUrl || enabledAudios[0].fileUrl || ''
    };
    
    // 设置为播放状态
    isPlaying.value = true;
    
    // 手动播放音频
    nextTick(() => {
      if (streamPlayerRef.value) {
        streamPlayerRef.value.play();
      }
    });
    
    loading.value = false;
  }).catch(error => {
    console.error("获取电台音频失败:", error);
    proxy.$modal.msgError("获取电台音频失败");
    loading.value = false;
  });
}

/** 音频播放事件 */
function handleAudioPlay() {
  isPlaying.value = true;
}

/** 音频暂停事件 */
function handleAudioPause() {
  isPlaying.value = false;
}

/** 音频错误事件 */
function handleAudioError(error) {
  console.error("音频播放错误:", error);
  
  // 使用错误对象中的友好中文消息
  const errorMessage = error.message || "音频播放失败，请稍后重试";
  
  // 显示更友好的错误提示
  proxy.$modal.msgError(errorMessage);
  
  isPlaying.value = false;
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// 统一的数据加载函数
function loadData() {
  // 先设置加载状态，确保UI显示加载中
  loading.value = true;
  
  // 使用setTimeout创建微任务，确保在DOM更新周期之后执行
  setTimeout(() => {
    nextTick(() => {
      // 重置分页为第一页
      queryParams.value.pageNum = 1;
      
      // 强制清空当前数据，触发视图更新
      radioList.value = [];
      
      // 使用新的日期工具函数添加日期范围参数
      const finalParams = addQueryDateRange(queryParams.value, dateRange.value);
      
      // 获取数据
      listRadio(finalParams).then(response => {
        // 强制更新UI
        nextTick(() => {
          radioList.value = response.rows;
          total.value = response.total;
          loading.value = false;
        });
      }).catch(error => {
        loading.value = false;
      });
    });
  }, 100); // 短暂延迟确保DOM已完全更新
}

// 组件挂载时执行
onMounted(() => {
  getList();
  // 初始化播放器状态
  resetPlayer();
  
  // 确保播放器处于暂停状态
  nextTick(() => {
    if (streamPlayerRef.value) {
      streamPlayerRef.value.pause();
    }
  });
});

// 使用watch监听路由变化，确保在标签页切换时重新加载数据
watch(() => route.fullPath, (newValue, oldValue) => {
  if (newValue && newValue.includes('/listen/radio')) {
    // 确保组件已挂载且路由确实改变了才重新加载
    if (oldValue !== newValue) {
      loadData();
    }
  }
}, { immediate: true });

// 使用defineExpose暴露刷新方法给父组件
defineExpose({
  refreshList: loadData
});

/** 获取行的类名 */
function getRowClassName({ row }) {
  return row.top === 1 ? 'top-row' : '';
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 120px;
}

/* 搜索容器样式 */
.search-container {
  margin-bottom: 10px;
}

.query-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 操作容器样式 */
.action-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 10px;
}

.player-wrapper {
  flex: 0 0 304px;
  height: 46px;
  position: relative;
}

/* 未连接状态的播放器样式 */
.inactive-player {
  opacity: 0.7;
}

.inactive-player::before {
  content: "未连接";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 2;
  pointer-events: none;
}

/* 全局播放器样式 */
.global-player {
  margin-bottom: 20px;
  background: var(--el-bg-color);
  border-radius: 4px;
  padding: 10px;
  box-shadow: var(--el-box-shadow-light);
  min-height: 80px;
}

.empty-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-player .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 电台名称单元格样式 */
.radio-name-cell {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保单选按钮组内容在一行显示 */
.inline-radio-group {
  display: flex;
  flex-wrap: nowrap;
  min-width: 200px;
}

/* 表格响应式样式 */
.el-table {
  width: 100%;
  margin-bottom: 20px;
}

/* 统一表格行高样式 - 参考post页面 */
.el-table :deep(.el-table__row) {
  height: 48px;
}

.el-table :deep(.el-table__cell) {
  padding: 12px 0;
  line-height: 24px;
}

@media screen and (max-width: 1200px) {
  .el-table .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .action-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .player-wrapper {
    width: 100%;
    flex: 0 0 auto;
  }
}

.image-container {
  position: relative;
  width: 50px;
  height: 50px;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
}

.table-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  transition: all 0.3s;
  border-radius: 4px;
}

.image-container:hover .table-image {
  transform: scale(1.05);
}

.image-hover-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.image-container:hover .image-hover-text {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #8c939d;
  font-size: 12px;
}

.image-placeholder .el-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #f56c6c;
  font-size: 12px;
}

.image-error .el-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.cover-image-preview, .bg-image-preview {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.cover-image-preview:hover, .bg-image-preview:hover {
  border-color: #409EFF;
}

/* 图片预览标题样式 */
.image-preview-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  margin-bottom: 10px;
}

/* 增强图片悬停效果 */
.image-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-container .image-hover-text {
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* 确保不会阻止点击事件 */
}

.custom-header {
  display: flex;
  align-items: center;
}

.custom-header .el-icon {
  margin-right: 8px;
}

.top-row {
  background-color: #ecf5ff !important;
  font-weight: 500;
}

.top-tag {
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  height: 24px;
  background-color: #409EFF;
  color: white;
  border-color: #409EFF;
}

.no-top-tag {
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  height: 24px;
  background-color: #f0f0f0;
  color: #606266;
  border-color: #dcdfe6;
}

.top-icon-el {
  margin-right: 4px;
  font-size: 14px;
}

/* 置顶按钮样式 */
.el-button.is-link.is-warning {
  color: #e6a23c;
}

.el-button.is-link.is-warning:hover {
  color: #f0c78a;
}

.el-button.is-link.is-warning:focus, .el-button.is-link.is-warning:active {
  color: #cf9236;
}

.top-icon-active {
  color: #e6a23c;
}

.top-button {
  position: relative;
}

/* 上传组件样式 */
.upload-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cover-uploader {
  margin-bottom: 8px;
}

.cover-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.cover-uploader .el-upload:hover {
  border-color: #409EFF;
}

.cover-upload-placeholder {
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #8c939d;
}

.cover-upload-placeholder .el-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.cover-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.player-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 10;
  cursor: not-allowed;
}
</style>


