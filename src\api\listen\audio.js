import request from '@/utils/request'

// 查询音频列表
export function listAudio(query) {
  return request({
    url: '/system/audio/list',
    method: 'get',
    params: query
  })
}

// 查询音频详细
export function getAudio(audioId) {
  return request({
    url: '/system/audio/' + audioId,
    method: 'get'
  })
}

// 新增音频
export function addAudio(data) {
  return request({
    url: '/system/audio',
    method: 'post',
    data: data
  })
}

// 修改音频
export function updateAudio(data) {
  return request({
    url: '/system/audio',
    method: 'put',
    data: data
  })
}

// 删除音频
export function delAudio(audioId) {
  return request({
    url: '/system/audio/' + audioId,
    method: 'delete'
  })
}

// 删除单个音频文件
export function delAudioFile(fileId) {
  return request({
    url: '/system/audio/file/' + fileId,
    method: 'delete'
  })
}

// 上传音频文件（单个或多个）
export function uploadAudioFile(file, onUploadProgress) {
  const formData = new FormData()
  
  if (Array.isArray(file)) {
    file.forEach(f => {
      formData.append('files', f)
    })
  } else {
    formData.append('files', file)
  }
  
  return request({
    url: '/system/audio/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
       skipRepeatSubmit: true
    },
    onUploadProgress
  })
}

// 批量上传并创建音频
export function uploadAndCreateAudio(files, radioStationId, onUploadProgress) {
  const formData = new FormData()
  if (Array.isArray(files)) {
    files.forEach(file => {
      formData.append('files', file)
    })
  } else {
    formData.append('files', files)
  }
  
  if (radioStationId) {
    formData.append('radioStationId', radioStationId)
  }
  
  return request({
    url: '/system/audio/uploadAndCreate',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

// 修改音频状态
export function changeAudioStatus(audioId, status) {
  const data = {
    id: audioId,
    status: status
  }
  return request({
    url: '/system/audio/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取电台下所有启用的音频
export function getRadioAudios(radioId) {
  return request({
    url: '/system/audio/radio/' + radioId,
    method: 'get'
  })
}

// 获取电台列表（用于下拉选择）
export function getRadioOptions() {
  return request({
    url: '/system/radio/list',
    method: 'get',
    params: { pageSize: 100 }
  })
} 