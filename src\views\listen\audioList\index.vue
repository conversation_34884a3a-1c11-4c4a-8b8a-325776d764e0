<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="query-form">
        <el-form-item label="音频编号" prop="id">
          <el-input
            v-model="queryParams.id"
            placeholder="请输入音频编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="音频名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入音频名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属电台" prop="radioId">
          <el-select
            v-model="queryParams.radioId"
            placeholder="请选择所属电台"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="item in radioOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="音频状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" style="width: 300px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-container">
      <div class="button-group">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
      
      <!-- 音频播放器组件 -->
      <div class="player-wrapper">
        <audio-player
          :audio-url="playList.length > 0 ? playList : ['']"
          :audio-name="playListNames.length > 0 ? playListNames : ['']"
          :initial-index="currentPlayIndex"
          :autoplay="playList.length > 0"
          :single-mode="true"
          @ended="onAudioEnded"
          @play="onAudioPlay"
          @pause="onAudioPause"
          @track-change="onTrackChange"
          ref="audioPlayerRef"
        />
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="audioList" style="width: 100%">
      <el-table-column label="音频编号" align="center" prop="id" width="200" />
      <el-table-column label="音频名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="120">
        <template #default="scope">
          <div class="audio-name-cell">
            <el-tooltip :content="scope.row.name" placement="top" :show-after="500">
              <span>{{ scope.row.name }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时长" align="center" width="100">
        <template #default="scope">
          <span class="duration-tag">{{ formatTime(scope.row.seconds || 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属电台" align="center" prop="radioStationName" :show-overflow-tooltip="true" min-width="120">
        <template #default="scope">
          <span class="radio-name-cell" :title="scope.row.radioStationName">{{ scope.row.radioStationName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="音频状态" align="center" width="100">
        <template #default="scope">
          <dict-tag :options="[
            { label: '启用', value: 1, elTagType: 'success' },
            { label: '禁用', value: 0, elTagType: 'danger' }
          ]" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" width="120">
        <template #default="scope">
          <div class="play-button-container">
            <el-tooltip :content="scope.row.status === 0 ? '音频已禁用，无法试听' : (currentAudio.id === scope.row.id && isPlaying ? '暂停' : '播放')" placement="top">
              <el-button 
                circle 
                :icon="currentAudio.id === scope.row.id && isPlaying ? 'VideoPause' : 'VideoPlay'" 
                @click="playAudio(scope.row)"
                size="small"
                :disabled="scope.row.status === 0"
                :type="scope.row.status === 0 ? 'info' : (currentAudio.id === scope.row.id && isPlaying ? 'danger' : 'primary')"
                class="play-button"
                :class="{'playing': currentAudio.id === scope.row.id && isPlaying, 'disabled': scope.row.status === 0}"
              />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="created" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.created) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updated" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updated) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="operation-buttons">
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:audio:remove']"></el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="audioList">
import { listAudio, delAudio } from "@/api/listen/audio";
import { listAllRadio } from "@/api/listen/radio";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import { onMounted, watch } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import { addQueryDateRange } from '@/utils/dateUtils';
import DictTag from '@/components/DictTag/index.vue';

const { proxy } = getCurrentInstance();
const route = useRoute();

const audioList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const dateRange = ref([]);

// 音频播放相关
const currentAudio = ref({
  id: null,
  name: '',
  url: '',
  audioUrl: '',
  fileUrl: '',
  duration: 0
});
const isPlaying = ref(false);

// 播放列表相关数据
const playList = ref([]);
const playListNames = ref([]);
const currentPlayIndex = ref(0);

// 电台选项列表
const radioOptions = ref([]);

// 引用播放器组件
const audioPlayerRef = ref(null);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    radioId: undefined,
    id: undefined,
    name: undefined,
    status: undefined,
    beginTime: undefined,
    endTime: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询音频列表 */
function getList() {
  loading.value = true;
  
  // 创建查询参数的副本
  const params = { ...queryParams.value };
  
  // 使用新的日期工具函数添加日期范围参数
  const finalParams = addQueryDateRange(params, dateRange.value);
  
  // 使用处理后的参数查询列表
  listAudio(finalParams).then(response => {
    audioList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error("获取音频列表失败:", error);
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  
  // 确保状态为数字类型
  if (queryParams.value.status !== undefined && queryParams.value.status !== '' && queryParams.value.status !== null) {
    queryParams.value.status = Number(queryParams.value.status);
  }
  
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  
  // 重置播放器状态
  resetPlayer();
  
  handleQuery();
}

/** 重置播放器 */
function resetPlayer() {
  // 如果正在播放，先暂停
  if (isPlaying.value && audioPlayerRef.value) {
    audioPlayerRef.value.pause();
  }
  
  // 重置所有播放器相关状态
  currentAudio.value = {
    id: null,
    name: '',
    url: '',
    audioUrl: '',
    fileUrl: '',
    duration: 0
  };
  isPlaying.value = false;
  playList.value = [];
  playListNames.value = [];
  currentPlayIndex.value = 0;
}

/** 初始化播放器 */
function initPlayer() {
  resetPlayer();
}

/** 获取所有电台列表 */
function getRadioOptions() {
  listAllRadio().then(response => {
    radioOptions.value = response.data || [];
  });
}

/** 播放音频 */
function playAudio(audio) {
  // 如果音频被禁用，不允许播放
  if (audio.status === 0) {
    return;
  }
  
  // 如果当前点击的是正在播放的音频，则暂停播放
  if (currentAudio.value.id === audio.id && isPlaying.value) {
    // 如果播放器组件存在，调用其暂停方法
    if (audioPlayerRef.value) {
      audioPlayerRef.value.pause();
    }
    isPlaying.value = false;
    return;
  }
  
  // 设置当前音频信息
  currentAudio.value = {
    id: audio.id,
    name: audio.name,
    // 统一使用audioUrl或fileUrl
    url: audio.audioUrl || audio.fileUrl || '',
    audioUrl: audio.audioUrl || '',
    fileUrl: audio.fileUrl || '',
    duration: audio.seconds || 0
  };
  
  // 初始化播放列表 - 这里我们只放入当前音频
  initSinglePlayList(audio);
  
  // 设置为播放状态
  isPlaying.value = true;
}

/** 初始化单曲播放列表 */
function initSinglePlayList(audio) {
  // 清空当前播放列表
  playList.value = [];
  playListNames.value = [];
  
  // 只将当前音频添加到播放列表
  playList.value = [audio.audioUrl || audio.fileUrl || ''];
  playListNames.value = [audio.name];
  
  // 设置当前播放索引为0（因为只有一首）
  currentPlayIndex.value = 0;
}

/** 音频播放事件 */
function onAudioPlay() {
  isPlaying.value = true;
}

/** 音频暂停事件 */
function onAudioPause() {
  isPlaying.value = false;
}

/** 音频播放结束事件 */
function onAudioEnded() {
  isPlaying.value = false;
}

/** 音轨切换事件 */
function onTrackChange(index) {
  // 由于我们使用单曲播放模式，这个回调应该不会被触发
  // 但为了兼容性，我们保留这个方法
  console.log('Track changed to:', index);
}

/** 删除按钮操作 */
function handleDelete(row) {
  const audioId = row.id;
  proxy.$modal.confirm('是否确认删除音频编号为"' + audioId + '"的数据项?').then(function() {
    return delAudio(audioId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

// 组件挂载时执行
onMounted(() => {
  initPlayer();
  // 加载电台列表用于筛选
  getRadioOptions();
  // 直接加载音频列表数据
  getList();
});

// 使用watch监听路由变化，确保在标签页切换时重新加载数据
watch(() => route.fullPath, (newValue, oldValue) => {
  if (newValue && newValue.includes('/listen/audioList')) {
    // 确保组件已挂载且路由确实改变了才重新加载
    if (oldValue !== newValue) {
      // 加载电台列表和音频数据
      getRadioOptions();
      getList();
    }
  }
}, { immediate: true });

// 使用defineExpose暴露刷新方法给父组件
defineExpose({
  refreshList: getList
});

/** 格式化时间 */
function formatTime(seconds) {
  if (!seconds) return '00:00';
  seconds = Math.floor(seconds);
  const minutes = Math.floor(seconds / 60);
  seconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/** 格式化文件大小 */
function formatFileSize(bytes) {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 120px;
}

/* 搜索容器样式 */
.search-container {
  margin-bottom: 10px;
}

/* 操作容器样式 */
.action-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 10px;
}

/* 音频表格样式 */
.audio-table {
  margin-top: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light);
}

/* 音频名称单元格样式 */
.audio-name-cell,
.radio-name-cell {
  display: flex;
  align-items: center;
  max-width: 100%;
  justify-content: center;
}

.audio-name-cell span,
.radio-name-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: default;
}

/* 时长标签样式 */
.duration-tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #f0f2f5;
  border-radius: 12px;
  font-family: monospace;
  font-size: 13px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 播放按钮容器 */
.play-button-container {
  display: flex;
  justify-content: center;
}

.play-button {
  transition: all 0.3s;
}

.play-button.playing {
  transform: scale(1.1);
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.4);
}

.play-button.disabled {
  opacity: 0.6;
}

/* 播放器样式 */
.player-wrapper {
  flex: 0 0 304px;
  height: 46px;
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .action-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .player-wrapper {
    width: 100%;
    flex: 0 0 auto;
  }
}

/* 表格响应式样式 */
.el-table {
  width: 100%;
  margin-bottom: 20px;
}

.el-table .el-table__row {
  height: 55px; /* 增加行高 */
}

.el-table .el-table__row td {
  padding-top: 8px;
  padding-bottom: 8px;
}

@media screen and (max-width: 1200px) {
  .el-table .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}
</style> 